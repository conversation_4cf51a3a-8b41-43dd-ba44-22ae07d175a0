import zipfile
import os

# Extract the LettuceDetect repository
with zipfile.ZipFile('LettuceDetect.zip', 'r') as zip_ref:
    zip_ref.extractall('.')

print("Extraction completed!")

# List the contents to verify
for root, dirs, files in os.walk('.'):
    level = root.replace('.', '').count(os.sep)
    indent = ' ' * 2 * level
    print(f'{indent}{os.path.basename(root)}/')
    subindent = ' ' * 2 * (level + 1)
    for file in files[:10]:  # Limit to first 10 files per directory
        print(f'{subindent}{file}')
    if len(files) > 10:
        print(f'{subindent}... and {len(files) - 10} more files')
